enum DayOfWeek { monday, tuesday, wednesday, thursday, friday, saturday, sunday }
enum AvailabilityType { preferred, available, unavailable, required }

class TimeSlot {
  final TimeOfDay startTime;
  final TimeOfDay endTime;

  TimeSlot({
    required this.startTime,
    required this.endTime,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      startTime: TimeOfDay(
        hour: json['startHour'] as int,
        minute: json['startMinute'] as int,
      ),
      endTime: TimeOfDay(
        hour: json['endHour'] as int,
        minute: json['endMinute'] as int,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startHour': startTime.hour,
      'startMinute': startTime.minute,
      'endHour': endTime.hour,
      'endMinute': endTime.minute,
    };
  }

  Duration get duration {
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    return Duration(minutes: endMinutes - startMinutes);
  }

  bool overlapsWith(TimeSlot other) {
    final thisStart = startTime.hour * 60 + startTime.minute;
    final thisEnd = endTime.hour * 60 + endTime.minute;
    final otherStart = other.startTime.hour * 60 + other.startTime.minute;
    final otherEnd = other.endTime.hour * 60 + other.endTime.minute;

    return thisStart < otherEnd && thisEnd > otherStart;
  }
}

class DayAvailability {
  final DayOfWeek day;
  final List<TimeSlot> timeSlots;
  final AvailabilityType type;
  final String? notes;

  DayAvailability({
    required this.day,
    required this.timeSlots,
    this.type = AvailabilityType.available,
    this.notes,
  });

  factory DayAvailability.fromJson(Map<String, dynamic> json) {
    return DayAvailability(
      day: DayOfWeek.values.firstWhere(
        (e) => e.toString() == 'DayOfWeek.${json['day']}',
      ),
      timeSlots: (json['timeSlots'] as List)
          .map((slot) => TimeSlot.fromJson(slot as Map<String, dynamic>))
          .toList(),
      type: AvailabilityType.values.firstWhere(
        (e) => e.toString() == 'AvailabilityType.${json['type']}',
        orElse: () => AvailabilityType.available,
      ),
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day.toString().split('.').last,
      'timeSlots': timeSlots.map((slot) => slot.toJson()).toList(),
      'type': type.toString().split('.').last,
      'notes': notes,
    };
  }

  bool get isAvailable => timeSlots.isNotEmpty;
  bool get isUnavailable => type == AvailabilityType.unavailable;
  bool get isPreferred => type == AvailabilityType.preferred;
  bool get isRequired => type == AvailabilityType.required;

  double get totalHours {
    return timeSlots.fold(0.0, (sum, slot) => sum + slot.duration.inMinutes / 60.0);
  }
}

class AvailabilityModel {
  final String id;
  final String employeeId;
  final String employeeName;
  final List<DayAvailability> weeklyAvailability;
  final DateTime validFrom;
  final DateTime validTo;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? notes;
  final Map<String, dynamic>? constraints;

  AvailabilityModel({
    required this.id,
    required this.employeeId,
    required this.employeeName,
    required this.weeklyAvailability,
    required this.validFrom,
    required this.validTo,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
    this.constraints,
  });

  factory AvailabilityModel.fromJson(Map<String, dynamic> json) {
    return AvailabilityModel(
      id: json['id'] as String,
      employeeId: json['employeeId'] as String,
      employeeName: json['employeeName'] as String,
      weeklyAvailability: (json['weeklyAvailability'] as List)
          .map((day) => DayAvailability.fromJson(day as Map<String, dynamic>))
          .toList(),
      validFrom: DateTime.parse(json['validFrom'] as String),
      validTo: DateTime.parse(json['validTo'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      notes: json['notes'] as String?,
      constraints: json['constraints'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'weeklyAvailability': weeklyAvailability.map((day) => day.toJson()).toList(),
      'validFrom': validFrom.toIso8601String(),
      'validTo': validTo.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'notes': notes,
      'constraints': constraints,
    };
  }

  DayAvailability? getAvailabilityForDay(DayOfWeek day) {
    try {
      return weeklyAvailability.firstWhere((availability) => availability.day == day);
    } catch (e) {
      return null;
    }
  }

  bool isAvailableOnDay(DayOfWeek day) {
    final availability = getAvailabilityForDay(day);
    return availability != null && availability.isAvailable;
  }

  List<TimeSlot> getTimeSlotsForDay(DayOfWeek day) {
    final availability = getAvailabilityForDay(day);
    return availability?.timeSlots ?? [];
  }

  double getTotalWeeklyHours() {
    return weeklyAvailability.fold(0.0, (sum, day) => sum + day.totalHours);
  }

  bool get isValid {
    final now = DateTime.now();
    return now.isAfter(validFrom) && now.isBefore(validTo);
  }

  AvailabilityModel copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    List<DayAvailability>? weeklyAvailability,
    DateTime? validFrom,
    DateTime? validTo,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
    Map<String, dynamic>? constraints,
  }) {
    return AvailabilityModel(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      weeklyAvailability: weeklyAvailability ?? this.weeklyAvailability,
      validFrom: validFrom ?? this.validFrom,
      validTo: validTo ?? this.validTo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
      constraints: constraints ?? this.constraints,
    );
  }
} 