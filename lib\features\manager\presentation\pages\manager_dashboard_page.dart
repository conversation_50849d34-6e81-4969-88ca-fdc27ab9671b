import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/providers/auth_provider.dart';
import '../../../../core/providers/roster_provider.dart';
import '../../../../core/models/shift_model.dart';
import '../../../../core/theme/app_theme.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/quick_action_button.dart';

class ManagerDashboardPage extends StatefulWidget {
  const ManagerDashboardPage({super.key});

  @override
  State<ManagerDashboardPage> createState() => _ManagerDashboardPageState();
}

class _ManagerDashboardPageState extends State<ManagerDashboardPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final rosterProvider =
          Provider.of<RosterProvider>(context, listen: false);
      rosterProvider.refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Manager Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Implement notifications
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Notifications coming soon')),
              );
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  // TODO: Navigate to profile
                  break;
                case 'settings':
                  // TODO: Navigate to settings
                  break;
                case 'logout':
                  _handleLogout();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person_outline),
                    SizedBox(width: 8),
                    Text('Profile'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings_outlined),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout),
                    SizedBox(width: 8),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          final rosterProvider =
              Provider.of<RosterProvider>(context, listen: false);
          await rosterProvider.refresh();
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  return Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.primaryColor,
                          AppTheme.primaryLight,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: Colors.white.withOpacity(0.2),
                          child: Icon(
                            Icons.person,
                            size: 30,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Welcome back,',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color: Colors.white.withOpacity(0.8),
                                    ),
                              ),
                              Text(
                                authProvider.currentUser?.name ?? 'Manager',
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineSmall
                                    ?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),

              const SizedBox(height: 24),

              // Quick Stats
              Text(
                'Quick Stats',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
              ),
              const SizedBox(height: 16),

              Consumer<RosterProvider>(
                builder: (context, rosterProvider, child) {
                  return GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.2,
                    children: [
                      DashboardCard(
                        title: 'Today\'s Shifts',
                        value: '${rosterProvider.todayShifts.length}',
                        icon: Icons.schedule,
                        color: AppTheme.primaryColor,
                        onTap: () => context.go('/manager/roster'),
                      ),
                      DashboardCard(
                        title: 'Active Employees',
                        value: '${rosterProvider.employees.length}',
                        icon: Icons.people,
                        color: AppTheme.secondaryColor,
                        onTap: () => context.go('/manager/employees'),
                      ),
                      DashboardCard(
                        title: 'Ongoing Shifts',
                        value: '${rosterProvider.ongoingShifts.length}',
                        icon: Icons.play_circle_outline,
                        color: AppTheme.accentColor,
                        onTap: () => context.go('/manager/roster'),
                      ),
                      DashboardCard(
                        title: 'Upcoming Shifts',
                        value: '${rosterProvider.upcomingShifts.length}',
                        icon: Icons.upcoming,
                        color: AppTheme.warningColor,
                        onTap: () => context.go('/manager/roster'),
                      ),
                    ],
                  );
                },
              ),

              const SizedBox(height: 32),

              // Quick Actions
              Text(
                'Quick Actions',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
              ),
              const SizedBox(height: 16),

              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.5,
                children: [
                  QuickActionButton(
                    title: 'Create Shift',
                    subtitle: 'Add new shift',
                    icon: Icons.add_circle_outline,
                    color: AppTheme.primaryColor,
                    onTap: () {
                      // TODO: Navigate to create shift
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('Create shift feature coming soon')),
                      );
                    },
                  ),
                  QuickActionButton(
                    title: 'AI Assistant',
                    subtitle: 'Get AI suggestions',
                    icon: Icons.psychology,
                    color: AppTheme.secondaryColor,
                    onTap: () => context.go('/manager/ai-assistant'),
                  ),
                  QuickActionButton(
                    title: 'View Roster',
                    subtitle: 'Manage schedules',
                    icon: Icons.calendar_month,
                    color: AppTheme.accentColor,
                    onTap: () => context.go('/manager/roster'),
                  ),
                  QuickActionButton(
                    title: 'Analytics',
                    subtitle: 'View reports',
                    icon: Icons.analytics,
                    color: AppTheme.warningColor,
                    onTap: () => context.go('/manager/analytics'),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Recent Activity
              Text(
                'Recent Activity',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
              ),
              const SizedBox(height: 16),

              Consumer<RosterProvider>(
                builder: (context, rosterProvider, child) {
                  final recentShifts = rosterProvider.shifts
                      .where((shift) => shift.startTime.isAfter(
                            DateTime.now().subtract(const Duration(days: 7)),
                          ))
                      .take(5)
                      .toList();

                  if (recentShifts.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppTheme.textTertiary.withOpacity(0.2),
                        ),
                      ),
                      child: Center(
                        child: Column(
                          children: [
                            Icon(
                              Icons.schedule_outlined,
                              size: 48,
                              color: AppTheme.textTertiary,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'No recent activity',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: AppTheme.textTertiary,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: recentShifts.length,
                    itemBuilder: (context, index) {
                      final shift = recentShifts[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor:
                                AppTheme.primaryColor.withOpacity(0.1),
                            child: Icon(
                              Icons.schedule,
                              color: AppTheme.primaryColor,
                              size: 20,
                            ),
                          ),
                          title: Text(
                            shift.employeeName,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                          subtitle: Text(
                            '${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')} - ${shift.endTime.hour}:${shift.endTime.minute.toString().padLeft(2, '0')}',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: AppTheme.textSecondary,
                                ),
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor(shift.status)
                                  .withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              shift.status
                                  .toString()
                                  .split('.')
                                  .last
                                  .toUpperCase(),
                              style: Theme.of(context)
                                  .textTheme
                                  .labelSmall
                                  ?.copyWith(
                                    color: _getStatusColor(shift.status),
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(ShiftStatus status) {
    switch (status) {
      case ShiftStatus.scheduled:
        return AppTheme.warningColor;
      case ShiftStatus.confirmed:
        return AppTheme.primaryColor;
      case ShiftStatus.completed:
        return AppTheme.successColor;
      case ShiftStatus.cancelled:
        return AppTheme.errorColor;
    }
  }

  Future<void> _handleLogout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.signOut();
    }
  }
}
