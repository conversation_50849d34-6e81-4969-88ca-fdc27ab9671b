import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;
import '../models/shift_model.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    // Request notification permissions
    await Permission.notification.request();

    // Initialize Android settings
    const AndroidInitializationSettings androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // Initialize iOS settings
    const DarwinInitializationSettings iosSettings =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    // Initialize settings
    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    // Initialize the plugin
    await _notifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  static void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    print('Notification tapped: ${response.payload}');
  }

  static Future<void> scheduleShiftReminder(ShiftModel shift) async {
    // Schedule reminder 30 minutes before shift
    final reminderTime = shift.startTime.subtract(const Duration(minutes: 30));

    if (reminderTime.isAfter(DateTime.now())) {
      await _notifications.zonedSchedule(
        shift.id.hashCode,
        'Shift Reminder',
        'Your shift starts in 30 minutes: ${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')}',
        _convertToTZDateTime(reminderTime),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'shift_reminders',
            'Shift Reminders',
            channelDescription: 'Notifications for shift reminders',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(),
        ),
        androidAllowWhileIdle: true,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: 'shift_reminder_${shift.id}',
      );
    }
  }

  static Future<void> scheduleShiftStartNotification(ShiftModel shift) async {
    await _notifications.zonedSchedule(
      (shift.id.hashCode + 1),
      'Shift Started',
      'Your shift has started: ${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')}',
      _convertToTZDateTime(shift.startTime),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'shift_start',
          'Shift Start',
          channelDescription: 'Notifications when shifts start',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
        ),
        iOS: DarwinNotificationDetails(),
      ),
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: 'shift_start_${shift.id}',
    );
  }

  static Future<void> scheduleShiftEndNotification(ShiftModel shift) async {
    await _notifications.zonedSchedule(
      (shift.id.hashCode + 2),
      'Shift Ending',
      'Your shift ends in 15 minutes: ${shift.endTime.hour}:${shift.endTime.minute.toString().padLeft(2, '0')}',
      _convertToTZDateTime(shift.endTime.subtract(const Duration(minutes: 15))),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'shift_end',
          'Shift End',
          channelDescription: 'Notifications when shifts are ending',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
        ),
        iOS: DarwinNotificationDetails(),
      ),
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: 'shift_end_${shift.id}',
    );
  }

  static Future<void> showShiftUpdateNotification(ShiftModel shift) async {
    await _notifications.show(
      shift.id.hashCode + 3,
      'Shift Updated',
      'Your shift has been updated: ${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')} - ${shift.endTime.hour}:${shift.endTime.minute.toString().padLeft(2, '0')}',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'shift_updates',
          'Shift Updates',
          channelDescription: 'Notifications for shift updates',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: 'shift_update_${shift.id}',
    );
  }

  static Future<void> showShiftCancelledNotification(ShiftModel shift) async {
    await _notifications.show(
      shift.id.hashCode + 4,
      'Shift Cancelled',
      'Your shift has been cancelled: ${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')} - ${shift.endTime.hour}:${shift.endTime.minute.toString().padLeft(2, '0')}',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'shift_cancellations',
          'Shift Cancellations',
          channelDescription: 'Notifications for cancelled shifts',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: 'shift_cancelled_${shift.id}',
    );
  }

  static Future<void> showNewShiftNotification(ShiftModel shift) async {
    await _notifications.show(
      shift.id.hashCode + 5,
      'New Shift Assigned',
      'You have been assigned a new shift: ${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')} - ${shift.endTime.hour}:${shift.endTime.minute.toString().padLeft(2, '0')}',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'new_shifts',
          'New Shifts',
          channelDescription: 'Notifications for new shift assignments',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: 'new_shift_${shift.id}',
    );
  }

  static Future<void> showWeeklyScheduleNotification() async {
    await _notifications.show(
      999999,
      'Weekly Schedule Available',
      'Your weekly schedule is now available. Check the app for details.',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'weekly_schedule',
          'Weekly Schedule',
          channelDescription: 'Notifications for weekly schedule availability',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: 'weekly_schedule',
    );
  }

  static Future<void> showAvailabilityReminderNotification() async {
    await _notifications.show(
      999998,
      'Update Your Availability',
      'Please update your availability for next week.',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'availability_reminder',
          'Availability Reminder',
          channelDescription: 'Reminders to update availability',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: 'availability_reminder',
    );
  }

  static Future<void> cancelShiftNotifications(String shiftId) async {
    await _notifications.cancel(shiftId.hashCode);
    await _notifications.cancel(shiftId.hashCode + 1);
    await _notifications.cancel(shiftId.hashCode + 2);
    await _notifications.cancel(shiftId.hashCode + 3);
    await _notifications.cancel(shiftId.hashCode + 4);
    await _notifications.cancel(shiftId.hashCode + 5);
  }

  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  static Future<List<PendingNotificationRequest>>
      getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  static tz.TZDateTime _convertToTZDateTime(DateTime dateTime) {
    // Convert to local timezone
    return tz.TZDateTime.from(dateTime, tz.local);
  }

  // Helper method to schedule multiple notifications for a shift
  static Future<void> scheduleAllShiftNotifications(ShiftModel shift) async {
    await scheduleShiftReminder(shift);
    await scheduleShiftStartNotification(shift);
    await scheduleShiftEndNotification(shift);
  }

  // Helper method to cancel all notifications for a shift
  static Future<void> cancelAllShiftNotifications(ShiftModel shift) async {
    await cancelShiftNotifications(shift.id);
  }
}
