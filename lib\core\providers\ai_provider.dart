import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/shift_model.dart';
import '../models/availability_model.dart';
import '../models/user_model.dart';

class AIProvider with ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  String? _aiResponse;
  List<ShiftModel> _suggestedShifts = [];

  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get aiResponse => _aiResponse;
  List<ShiftModel> get suggestedShifts => _suggestedShifts;

  // AI Configuration
  static const String _baseUrl = 'https://api.openai.com/v1/chat/completions';
  static const String _model = 'gpt-4';
  
  // You'll need to set this up with your OpenAI API key
  static const String _apiKey = 'YOUR_OPENAI_API_KEY';

  Future<List<ShiftModel>> generateAISuggestions({
    required List<UserModel> employees,
    required List<AvailabilityModel> availabilities,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> constraints,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final prompt = _buildPrompt(
        employees: employees,
        availabilities: availabilities,
        startDate: startDate,
        endDate: endDate,
        constraints: constraints,
      );

      final response = await _callOpenAI(prompt);
      final suggestions = _parseAISuggestions(response, employees);
      
      _suggestedShifts = suggestions;
      _aiResponse = response;
      
      return suggestions;
    } catch (e) {
      _error = 'Failed to generate AI suggestions: $e';
      return [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<String> getAIAnalysis({
    required List<ShiftModel> shifts,
    required List<UserModel> employees,
    required List<AvailabilityModel> availabilities,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final prompt = _buildAnalysisPrompt(
        shifts: shifts,
        employees: employees,
        availabilities: availabilities,
      );

      final response = await _callOpenAI(prompt);
      _aiResponse = response;
      
      return response;
    } catch (e) {
      _error = 'Failed to get AI analysis: $e';
      return 'Failed to analyze roster.';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<List<ShiftModel>> optimizeRoster({
    required List<ShiftModel> currentShifts,
    required List<UserModel> employees,
    required List<AvailabilityModel> availabilities,
    required Map<String, dynamic> optimizationGoals,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final prompt = _buildOptimizationPrompt(
        currentShifts: currentShifts,
        employees: employees,
        availabilities: availabilities,
        optimizationGoals: optimizationGoals,
      );

      final response = await _callOpenAI(prompt);
      final optimizedShifts = _parseAISuggestions(response, employees);
      
      _suggestedShifts = optimizedShifts;
      _aiResponse = response;
      
      return optimizedShifts;
    } catch (e) {
      _error = 'Failed to optimize roster: $e';
      return [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<String> _callOpenAI(String prompt) async {
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $_apiKey',
    };

    final body = {
      'model': _model,
      'messages': [
        {
          'role': 'system',
          'content': 'You are an expert workforce management and scheduling assistant. You help create optimal work schedules that balance business needs with employee preferences and constraints.',
        },
        {
          'role': 'user',
          'content': prompt,
        },
      ],
      'max_tokens': 2000,
      'temperature': 0.7,
    };

    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: headers,
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['choices'][0]['message']['content'];
    } else {
      throw Exception('OpenAI API error: ${response.statusCode} - ${response.body}');
    }
  }

  String _buildPrompt({
    required List<UserModel> employees,
    required List<AvailabilityModel> availabilities,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> constraints,
  }) {
    final employeeInfo = employees.map((emp) => 
      'Employee: ${emp.name} (ID: ${emp.id}) - ${emp.department ?? "No department"}'
    ).join('\n');

    final availabilityInfo = availabilities.map((avail) {
      final employee = employees.firstWhere((emp) => emp.id == avail.employeeId);
      return '${employee.name}: ${avail.weeklyAvailability.map((day) => 
        '${day.day.toString().split('.').last}: ${day.timeSlots.map((slot) => 
          '${slot.startTime.format(context)}-${slot.endTime.format(context)}'
        ).join(', ')}'
      ).join('; ')}';
    }).join('\n');

    return '''
Create an optimal work schedule for the following period: ${startDate.toString().split(' ')[0]} to ${endDate.toString().split(' ')[0]}

Employees:
$employeeInfo

Availability:
$availabilityInfo

Constraints:
${constraints.entries.map((e) => '${e.key}: ${e.value}').join('\n')}

Please provide a JSON array of shifts with the following structure:
[
  {
    "employeeId": "employee_id",
    "employeeName": "Employee Name",
    "startTime": "YYYY-MM-DDTHH:MM:SS",
    "endTime": "YYYY-MM-DDTHH:MM:SS",
    "type": "morning|afternoon|evening|night|custom",
    "notes": "optional notes"
  }
]

Consider:
1. Employee availability and preferences
2. Fair distribution of shifts
3. Compliance with labor laws
4. Business requirements
5. Employee workload balance

Return only the JSON array, no additional text.
''';
  }

  String _buildAnalysisPrompt({
    required List<ShiftModel> shifts,
    required List<UserModel> employees,
    required List<AvailabilityModel> availabilities,
  }) {
    final shiftSummary = shifts.map((shift) => 
      '${shift.employeeName}: ${shift.startTime.toString().split(' ')[0]} ${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')}-${shift.endTime.hour}:${shift.endTime.minute.toString().padLeft(2, '0')} (${shift.hoursWorked.toStringAsFixed(1)}h)'
    ).join('\n');

    final employeeHours = <String, double>{};
    for (final shift in shifts) {
      employeeHours[shift.employeeName] = (employeeHours[shift.employeeName] ?? 0) + shift.hoursWorked;
    }

    final hoursSummary = employeeHours.entries.map((e) => 
      '${e.key}: ${e.value.toStringAsFixed(1)} hours'
    ).join('\n');

    return '''
Analyze the following work schedule and provide insights:

Shifts:
$shiftSummary

Total Hours per Employee:
$hoursSummary

Please provide:
1. Workload distribution analysis
2. Potential conflicts with availability
3. Fairness assessment
4. Suggestions for improvement
5. Compliance considerations

Provide a concise, actionable analysis.
''';
  }

  String _buildOptimizationPrompt({
    required List<ShiftModel> currentShifts,
    required List<UserModel> employees,
    required List<AvailabilityModel> availabilities,
    required Map<String, dynamic> optimizationGoals,
  }) {
    final currentSummary = currentShifts.map((shift) => 
      '${shift.employeeName}: ${shift.startTime.toString().split(' ')[0]} ${shift.startTime.hour}:${shift.startTime.minute.toString().padLeft(2, '0')}-${shift.endTime.hour}:${shift.endTime.minute.toString().padLeft(2, '0')}'
    ).join('\n');

    return '''
Optimize the following work schedule based on these goals:
${optimizationGoals.entries.map((e) => '${e.key}: ${e.value}').join('\n')}

Current Schedule:
$currentSummary

Please provide an optimized JSON array of shifts that addresses the optimization goals while respecting employee availability and preferences.

Return only the JSON array, no additional text.
''';
  }

  List<ShiftModel> _parseAISuggestions(String response, List<UserModel> employees) {
    try {
      // Extract JSON from response (in case there's additional text)
      final jsonStart = response.indexOf('[');
      final jsonEnd = response.lastIndexOf(']') + 1;
      
      if (jsonStart == -1 || jsonEnd == 0) {
        throw Exception('No valid JSON found in response');
      }
      
      final jsonString = response.substring(jsonStart, jsonEnd);
      final List<dynamic> jsonData = jsonDecode(jsonString);
      
      return jsonData.map((shiftData) {
        final employee = employees.firstWhere(
          (emp) => emp.id == shiftData['employeeId'],
          orElse: () => employees.first,
        );
        
        return ShiftModel(
          id: '', // Will be set when saved
          employeeId: shiftData['employeeId'],
          employeeName: shiftData['employeeName'] ?? employee.name,
          startTime: DateTime.parse(shiftData['startTime']),
          endTime: DateTime.parse(shiftData['endTime']),
          type: ShiftType.values.firstWhere(
            (type) => type.toString().split('.').last == shiftData['type'],
            orElse: () => ShiftType.custom,
          ),
          notes: shiftData['notes'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to parse AI suggestions: $e');
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearSuggestions() {
    _suggestedShifts.clear();
    _aiResponse = null;
    notifyListeners();
  }
} 